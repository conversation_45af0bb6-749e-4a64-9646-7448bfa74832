import React from 'react'

interface DynamicBackgroundProps {
  /** 是否显示背景图片 */
  showBackgroundImage?: boolean
  /** 背景图片URL */
  backgroundImageUrl?: string
  /** 自定义类名 */
  className?: string
  /** 子元素 */
  children?: React.ReactNode
  /** 背景模糊强度 (0-20, 默认为 2) */
  blurIntensity?: number
}

export const DynamicBackground: React.FC<DynamicBackgroundProps> = ({
  showBackgroundImage = false,
  backgroundImageUrl,
  className = '',
  children,
  blurIntensity = 20,
}) => {
  return (
    <div
      className={`fixed inset-0 w-full h-full overflow-hidden bg-black ${className}`}
    >
      {/* 模糊背景层 */}
      <div
        className="absolute inset-0 w-full h-full"
        style={{ filter: `blur(${blurIntensity}px)` }}
      >
        {/* 背景图片 */}
        {showBackgroundImage && backgroundImageUrl && (
          <div
            className="absolute inset-0 bg-center bg-cover"
            style={{ backgroundImage: `url('${backgroundImageUrl}')` }}
          />
        )}

        {/* 粉色光晕多层叠加 */}
        <div
          className="absolute rounded-full pointer-events-none"
          style={{
            width: '900px',
            height: '900px',
            top: '25%',
            left: '75%',
            transform: 'translate(-50%, -50%)',
            animation: 'breathePink 4s ease-in-out infinite',
          }}
        >
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(255, 20, 147, 0.6)',
              width: '900px',
              height: '900px',
              filter: 'blur(80px)',
              top: 0,
              left: 0,
            }}
          />
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(255, 20, 147, 0.3)',
              width: '600px',
              height: '600px',
              filter: 'blur(40px)',
              top: '150px',
              left: '150px',
            }}
          />
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(255, 20, 147, 0.15)',
              width: '300px',
              height: '300px',
              filter: 'blur(20px)',
              top: '300px',
              left: '300px',
            }}
          />
        </div>

        {/* 紫色光晕多层叠加 */}
        <div
          className="absolute rounded-full pointer-events-none"
          style={{
            width: '1500px',
            height: '1500px',
            top: '90%',
            left: '30%',
            transform: 'translate(-50%, -50%)',
            animation: 'breathePurple 5.5s ease-in-out infinite',
          }}
        >
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(138, 43, 226, 0.6)',
              width: '1500px',
              height: '1500px',
              filter: 'blur(110px)',
              top: 0,
              left: 0,
            }}
          />
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(186, 85, 211, 0.3)',
              width: '1000px',
              height: '1000px',
              filter: 'blur(60px)',
              top: '250px',
              left: '250px',
            }}
          />
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(138, 43, 226, 0.15)',
              width: '600px',
              height: '600px',
              filter: 'blur(30px)',
              top: '450px',
              left: '450px',
            }}
          />
        </div>

        {/* 橙色光晕多层叠加 */}
        <div
          className="absolute rounded-full pointer-events-none"
          style={{
            width: '800px',
            height: '800px',
            top: '15%',
            left: '30%',
            transform: 'translate(-50%, -50%)',
            animation: 'breatheOrange 4s ease-in-out infinite',
            animationDelay: '1.5s',
          }}
        >
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(255, 140, 0, 0.6)',
              width: '800px',
              height: '800px',
              filter: 'blur(70px)',
              top: 0,
              left: 0,
            }}
          />
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(255, 200, 0, 0.3)',
              width: '550px',
              height: '550px',
              filter: 'blur(40px)',
              top: '150px',
              left: '150px',
            }}
          />
          <div
            className="absolute rounded-full"
            style={{
              backgroundColor: 'rgba(255, 140, 0, 0.15)',
              width: '300px',
              height: '300px',
              filter: 'blur(20px)',
              top: '300px',
              left: '300px',
            }}
          />
        </div>
      </div>

      {/* 子元素内容 */}
      {children && (
        <div className="relative z-10 w-full h-full">{children}</div>
      )}

      {/* 动画样式 */}
      <style>{`
        @keyframes breathePink {
          0%, 100% { opacity: 0.25; transform: scale(1) translate(-50%, -50%) rotate(0deg); }
          30% { opacity: 0.75; transform: scale(1.15) translate(-50%, -50%) rotate(5deg); }
          70% { opacity: 0.5; transform: scale(0.95) translate(-50%, -50%) rotate(-4deg); }
        }
        @keyframes breathePurple {
          0%, 100% { opacity: 0.3; transform: scale(1) translate(-50%, -50%) rotate(0deg); }
          40% { opacity: 0.8; transform: scale(1.35) translate(-50%, -50%) rotate(-6deg); }
          70% { opacity: 0.6; transform: scale(1.05) translate(-50%, -50%) rotate(4deg); }
        }
        @keyframes breatheOrange {
          0%, 100% { opacity: 0.45; transform: scale(1) translate(-50%, -50%) rotate(0deg); }
          20% { opacity: 0.6; transform: scale(1.25) translate(-50%, -50%) rotate(6deg); }
          60% { opacity: 0.5; transform: scale(1) translate(-50%, -50%) rotate(-5deg); }
        }
      `}</style>
    </div>
  )
}
